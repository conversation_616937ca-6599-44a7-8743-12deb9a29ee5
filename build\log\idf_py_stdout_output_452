-- ccache will be used for faster recompilation
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32s3
NOTICE: Processing 1 dependencies:
NOTICE: [1/1] idf (5.4.1)
-- Project sdkconfig file C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/sdkconfig
-- Compiler supported targets: xtensa-esp-elf
-- App "24_touch" version: 1
-- Adding linker script C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Components: BSP app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_security esp_system esp_timer esp_vfs_console esp_wifi espcoredump esptool_py fatfs freertos guider hal heap http_parser idf_test ieee802154 json log lvgl lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread rt sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/components/BSP D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/app_trace D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/app_update D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bt D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/cmock D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/cxx D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/efuse D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_app_format D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_bootloader_format D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_coex D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ana_cmpr D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_cam D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_dac D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gpio D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gptimer D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2c D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2s D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_isp D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_jpeg D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ledc D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_mcpwm D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_parlio D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_pcnt D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ppa D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_rmt D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdio D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdm D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdmmc D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdspi D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_spi D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_touch_sens D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_tsens D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_uart D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_usb_serial_jtag D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_eth D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_gdbstub D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hid D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_client D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_server D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_https_ota D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_https_server D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_local_ctrl D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_mm D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif_stack D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_partition D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_pm D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_psram D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_ringbuf D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_security D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_vfs_console D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/espcoredump D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esptool_py D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/components/guider D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/heap D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/http_parser D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/idf_test D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/ieee802154 D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/json D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/log C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/components/lvgl D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/main D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_sec_provider D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/openthread D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/perfmon D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protobuf-c D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/pthread D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/rt D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/sdmmc D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spiffs D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/touch_element D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/ulp D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/unity D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/usb D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/vfs D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wear_levelling D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa
-- Configuring done (9.9s)
-- Generating done (1.8s)
-- Build files have been written to: C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build
