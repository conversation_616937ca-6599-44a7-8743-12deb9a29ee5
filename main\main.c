
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <inttypes.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_system.h"
#include "esp_timer.h"
#include "nvs_flash.h"
#include "esp_err.h"
#include "esp_log.h"
#include "led.h"
#include "ltdc.h"
#include "touch.h"
#include "iic.h"
#include "xl9555.h"
#include "lvgl.h"
#include "lv_port_disp.h"
#include "lv_port_indev.h"
#include "gui_guider.h"


i2c_obj_t i2c0_master;

/* GUI Guider UI对象 */
lv_ui guider_ui;

/* LVGL 时基定时器句柄 */
static esp_timer_handle_t lvgl_tick_timer = NULL;

/**
 * @brief       LVGL时基定时器回调函数，每1ms触发
 * @param       arg: 参数（未使用）
 * @retval      无
 */
static void lv_tick_task(void *arg)
{
    lv_tick_inc(1);  /* LVGL时基递增1ms */
}

/**
 * @brief       初始化LVGL时基定时器
 * @param       无
 * @retval      无
 */
void lvgl_tick_timer_init(void)
{
    const esp_timer_create_args_t timer_args = {
        .callback = &lv_tick_task,
        .arg = NULL,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "lv_tick_timer"
    };

    esp_timer_create(&timer_args, &lvgl_tick_timer);
    esp_timer_start_periodic(lvgl_tick_timer, 1000); /* 1ms触发 */
}

/**
 * @brief       程序入口
 * @param       无
 * @retval      无
 */
void app_main(void)
{
    esp_err_t ret;

    ret = nvs_flash_init();             /* 初始化NVS */

    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }

    i2c0_master = iic_init(I2C_NUM_0);  /* 初始化IIC0 */
    xl9555_init(i2c0_master);           /* 初始化XL9555 */

    /* LVGL初始化 */
    lvgl_tick_timer_init();             /* 初始化LVGL时基 */
    lv_init();                          /* 初始化LVGL图形库 */
    lv_port_disp_init();                /* LVGL显示接口初始化,放在lv_init()的后面 */
    lv_port_indev_init();               /* LVGL输入接口初始化,放在lv_init()的后面 */

    /* 初始化GUI Guider界面 */
    setup_ui(&guider_ui);               /* 设置GUI Guider界面 */

    /* 启用LVGL自带的性能监控 */
   
    /* 使用LVGL内置的性能监控功能 */
    lv_obj_t * perf_monitor = lv_obj_create(lv_scr_act());
    lv_obj_set_size(perf_monitor, 120, 60);
    lv_obj_align(perf_monitor, LV_ALIGN_BOTTOM_RIGHT, -10, -10);
    lv_obj_set_style_bg_opa(perf_monitor, LV_OPA_70, 0);
    lv_obj_set_style_bg_color(perf_monitor, lv_color_black(), 0);

    /* 创建性能监控标签 */
    lv_obj_t * perf_label = lv_label_create(perf_monitor);
    lv_label_set_text(perf_label, "FPS Monitor");
    lv_obj_center(perf_label);
    lv_obj_set_style_text_color(perf_label, lv_color_white(), 0);
  

    /* LVGL主循环 */
    #if LV_USE_PERF_MONITOR
    static uint32_t frame_count = 0;
    static uint32_t last_time = 0;
    #endif

    while (1)
    {
        lv_task_handler();              /* LVGL任务管理 */

        #if LV_USE_PERF_MONITOR
        /* 更新性能监控显示 */
        frame_count++;
        uint32_t current_time = esp_timer_get_time() / 1000; /* 获取当前时间(ms) */
        if (current_time - last_time >= 1000) { /* 每秒更新一次 */
            uint32_t fps = frame_count * 1000 / (current_time - last_time);
            char fps_text[32];
            snprintf(fps_text, sizeof(fps_text), "FPS: %u", (unsigned int)fps);
            lv_label_set_text(perf_label, fps_text);
            frame_count = 0;
            last_time = current_time;
        }
        #endif

        vTaskDelay(pdMS_TO_TICKS(5));  /* 延迟5ms */
    }
}
